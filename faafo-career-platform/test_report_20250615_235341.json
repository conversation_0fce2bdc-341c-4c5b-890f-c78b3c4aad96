{"timestamp": "2025-06-15T23:53:41.995925", "summary": {"passed": 9, "failed": 9, "warnings": 0, "errors": 0, "total_execution_time": 43.948365449905396}, "results": [{"test_name": "page_structure", "status": "FAILED", "severity": "MEDIUM", "details": "No H1 heading found", "execution_time": 0.1856520175933838, "recommendations": ["Add exactly one H1 heading per page"], "screenshot_path": ""}, {"test_name": "accessibility_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.188737154006958, "recommendations": [], "screenshot_path": ""}, {"test_name": "forms_advanced", "status": "FAILED", "severity": "HIGH", "details": "AI-tested 1 forms | Insights: 6 | Issues: 4 | AI: Tested standard case for email; Tested standard case for email; Tested standard case for email", "execution_time": 2.528381109237671, "recommendations": [], "screenshot_path": ""}, {"test_name": "navigation_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.0544130802154541, "recommendations": ["Consider adding breadcrumb navigation for deep site structures", "Add skip navigation link for accessibility"], "screenshot_path": ""}, {"test_name": "responsive_design", "status": "FAILED", "severity": "MEDIUM", "details": "Touch targets too small (< 44px)", "execution_time": 1.5888597965240479, "recommendations": ["Increase touch target size to at least 44x44px"], "screenshot_path": ""}, {"test_name": "performance_comprehensive", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.008942127227783203, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_comprehensive", "status": "FAILED", "severity": "HIGH", "details": "Page not served over HTTPS", "execution_time": 0.19105291366577148, "recommendations": ["Implement SSL/TLS encryption"], "screenshot_path": ""}, {"test_name": "seo_basics", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.034204721450805664, "recommendations": ["Consider adding structured data for better SEO"], "screenshot_path": ""}, {"test_name": "browser_compatibility", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.0069980621337890625, "recommendations": [], "screenshot_path": ""}, {"test_name": "user_experience", "status": "PASSED", "severity": "LOW", "details": "", "execution_time": 0.21661782264709473, "recommendations": [], "screenshot_path": ""}, {"test_name": "security_advanced", "status": "FAILED", "severity": "CRITICAL", "details": "XSS vulnerability detected", "execution_time": 15.676499843597412, "recommendations": ["Implement input sanitization", "Use parameterized queries", "Add CSRF protection", "Implement proper access controls"], "screenshot_path": ""}, {"test_name": "malicious_inputs", "status": "FAILED", "severity": "HIGH", "details": "Tested 30 malicious inputs | Issues: 2", "execution_time": 6.574810028076172, "recommendations": ["Implement robust input validation", "Add length limits to inputs", "Sanitize user inputs", "Use content security policy"], "screenshot_path": ""}, {"test_name": "session_security", "status": "FAILED", "severity": "HIGH", "details": "Performed 3 session tests | Issues: 1", "execution_time": 5.202573776245117, "recommendations": ["Implement proper session regeneration", "Add session timeout mechanisms", "Validate session data integrity", "Use secure session storage"], "screenshot_path": ""}, {"test_name": "edge_case_authentication", "status": "PASSED", "severity": "MEDIUM", "details": "Tested 5 auth edge cases | Issues: 0", "execution_time": 4.6711390018463135, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_boundary_conditions", "status": "PASSED", "severity": "LOW", "details": "Tested 5 boundary conditions | Issues: 0", "execution_time": 2.727069854736328, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_concurrent_operations", "status": "PASSED", "severity": "LOW", "details": "Tested 3 concurrent operations | Issues: 0", "execution_time": 3.7191481590270996, "recommendations": [], "screenshot_path": ""}, {"test_name": "edge_case_error_handling", "status": "FAILED", "severity": "HIGH", "details": "Tested 3 error handling cases | Issues: 3", "execution_time": 0.10604310035705566, "recommendations": ["Implement proper 404 error pages", "Add path traversal protection", "Implement error boundaries", "Add proper error logging"], "screenshot_path": ""}, {"test_name": "ai_comprehensive_analysis", "status": "FAILED", "severity": "CRITICAL", "details": "AI analyzed 8 categories | Critical: 4 | Vulnerabilities: 3 | Recommendations: 10 | Key findings: Usability: Forms may be missing CSRF protection; Security: Basic security checks recommended; Accessibility: Accessibility review needed | Security issues: Potential CSRF vulnerability; Inline scripts detected - potential XSS risk", "execution_time": 0.*****************, "recommendations": ["Test login with invalid credentials", "Test password reset functionality", "Test account lockout mechanisms", "Test session management", "Test signup with duplicate email", "Test email validation", "Test password strength requirements", "Test input sanitization", "Test boundary conditions", "Test malformed inputs"], "screenshot_path": "screenshots/screenshot_ai_analysis_20250615_235341.png"}]}